FROM mcr.microsoft.com/playwright:v1.51.1-noble

# 安装编译工具
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    && rm -rf /var/lib/apt/lists/*

RUN npm install -g pnpm

WORKDIR /app/ai-assistant

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/design-ai-utils packages/design-ai-utils
COPY packages/tw-templates packages/tw-templates
COPY packages/image-to-design packages/image-to-design
COPY packages/server packages/server

# 在容器内安装所有依赖
RUN pnpm install --frozen-lockfile

# 构建项目
RUN cd packages/server && pnpm build

RUN chown -R pwuser:pwuser packages/server/
RUN chmod -R 777 packages/server/

RUN mkdir -p logs
RUN chown -R pwuser:pwuser logs
RUN chmod -R 777 logs

EXPOSE 7120

CMD ["node", "packages/server/dist/main.js"]
