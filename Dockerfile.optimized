FROM mcr.microsoft.com/playwright:v1.51.1-noble

# 安装编译工具
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app/ai-assistant

# 复制 package.json 文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/bm25-encoder/package.json packages/bm25-encoder/
COPY packages/server/package.json packages/server/

# 安装 pnpm
RUN npm install -g pnpm

# 安装依赖（这会在容器内编译原生模块）
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY packages/design-ai-utils packages/design-ai-utils
COPY packages/tw-templates packages/tw-templates
COPY packages/image-to-design packages/image-to-design
COPY packages/bm25-encoder packages/bm25-encoder
COPY packages/server packages/server

# 构建项目
RUN cd packages/server && pnpm build

RUN chown -R pwuser:pwuser packages/server/
RUN chmod -R 777 packages/server/

RUN mkdir -p logs
RUN chown -R pwuser:pwuser logs
RUN chmod -R 777 logs

EXPOSE 7120

CMD ["node", "packages/server/dist/main.js"]
